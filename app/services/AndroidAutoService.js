import { NativeModules, DeviceEventEmitter, Platform } from 'react-native';

const Native = Platform.OS === 'android' ? NativeModules.CarAutoModule : null;

const listeners = new Map();

const AndroidAutoService = {
  async isConnected() {
    if (!Native) return false;
    try {
      return await Native.isCarPlayConnected();
    } catch (e) {
      return false;
    }
  },

  async getState() {
    if (!Native) return { connected: false, foreground: false, carMode: false };
    try {
      return await Native.getCarPlayState();
    } catch (e) {
      return { connected: false, foreground: false, carMode: false };
    }
  },

  // Subscribe to updates: 'carModeChanged' and 'appForegroundChanged'
  addListener(event, callback) {
    if (Platform.OS !== 'android' || !Native) return { remove: () => {} };
    if (!['carModeChanged', 'appForegroundChanged'].includes(event)) {
      console.warn(`AndroidAutoService: unsupported event '${event}'`);
    }
    const sub = DeviceEventEmitter.addListener(event, payload => {
      callback(payload?.value === undefined ? payload : payload.value);
    });
    listeners.set(callback, sub);
    return {
      remove: () => {
        const s = listeners.get(callback);
        if (s) {
          s.remove();
          listeners.delete(callback);
        }
      },
    };
  },

  removeListener(callback) {
    const s = listeners.get(callback);
    if (s) {
      s.remove();
      listeners.delete(callback);
    }
  },
};

export default AndroidAutoService;
