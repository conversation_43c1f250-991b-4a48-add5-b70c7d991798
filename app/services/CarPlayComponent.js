import React, { useEffect, useState, useCallback, useRef } from 'react';
import { NativeEventEmitter, NativeModules, View } from 'react-native';
import {
  CarPlay,
  AlertTemplate,
  ActionSheetTemplate,
  InformationTemplate,
} from 'react-native-carplay';
import { useSelector } from 'react-redux';
import { sendErrorReport } from '../utils/commonFunction';

// Safe destructuring to avoid undefined errors
const MaxRCTCarPlayNotificationManager =
  NativeModules.MaxRCTCarPlayNotificationManager || null;

if (!MaxRCTCarPlayNotificationManager) {
  console.log(
    'Available modules:',
    Object.keys(NativeModules).filter(key => key.includes('CarPlay')),
  );
}

const CarPlayComponent = props => {
  const { navigation } = props;
  const user = useSelector(state => state.auth.userData);
  const { alertData, activeChildDetail } = useSelector(
    state => state.bluetooth,
  );
  const pendingNotification = useRef(null);
  const [carPlayConnected, setCarPlayConnected] = useState(false);
  const [carPlayForeground, setCarPlayForeground] = useState(false);
  const [carPlayStatus, setCarPlayStatus] = useState('disconnected');
  console.log('🚀 ~ CarPlayComponent ~ carPlayForeground:', carPlayForeground);
  console.log('🚀 ~ CarPlayComponent ~ carPlayConnected:', carPlayConnected);
  console.log('🚀 ~ CarPlayComponent ~ carPlayStatus:', carPlayStatus);
  const carPlayForegroundRef = useRef(carPlayForeground);

  // Check CarPlay connection status on mount
  useEffect(() => {
    const checkCarPlayConnection = async () => {
      try {
        // Check react-native-carplay status first
        const rnCarPlayConnected = CarPlay.connected;
        console.log('🚗 RN CarPlay Connected:', rnCarPlayConnected);
        setCarPlayConnected(rnCarPlayConnected);
        sendErrorReport(rnCarPlayConnected, 'rnCarPlayConnected');

        // Check native CarPlay connection status
        if (MaxRCTCarPlayNotificationManager) {
          try {
            const status =
              await MaxRCTCarPlayNotificationManager.getCarPlayConnectionStatus();
            console.log('🚗 CarPlay Status from Native:', status);
            setCarPlayConnected(status.connected || rnCarPlayConnected);
            setCarPlayForeground(status.foreground);
            setCarPlayStatus(
              status.connected
                ? status.foreground
                  ? 'foreground'
                  : 'background'
                : 'disconnected',
            );
            sendErrorReport(status, 'carplay_initial_status');
          } catch (nativeError) {
            console.log(
              'ℹ️ Native status check failed, using RN CarPlay status',
            );
            sendErrorReport(nativeError, 'carplay_nativeError');
            setCarPlayStatus(
              rnCarPlayConnected ? 'foreground' : 'disconnected',
            );
          }
        } else {
          // Fallback to RN CarPlay status
          setCarPlayStatus(rnCarPlayConnected ? 'foreground' : 'disconnected');
          setCarPlayForeground(rnCarPlayConnected);
        }

        // Handle pending notifications
        if (rnCarPlayConnected && pendingNotification.current) {
          handleNotification(pendingNotification.current);
          pendingNotification.current = null;
        }
      } catch (error) {
        console.error('❌ Error checking CarPlay status:', error);
        sendErrorReport(error, 'carplay_status_check_error');
      }
    };

    checkCarPlayConnection();
  }, []);

  // Add this useEffect to monitor state changes
  useEffect(() => {
    // If there's a mismatch, log it
    if (CarPlay.connected !== carPlayConnected) {
      console.warn('⚠️ State mismatch detected!');
      console.log('  - Native says:', CarPlay.connected);
      console.log('  - State says:', carPlayConnected);
    }
  }, [carPlayConnected]);

  // Connection handlers
  const onConnect = useCallback(() => {
    console.log('🔗 RN CarPlay onConnect triggered');
    sendErrorReport('true', 'carplay_connected_onconnect');
    setCarPlayConnected(true);
    setCarPlayForeground(true);
    setCarPlayStatus('foreground');

    if (pendingNotification.current) {
      // Use setTimeout to ensure state has updated
      setTimeout(() => {
        handleNotification(pendingNotification.current);
        pendingNotification.current = null;
      }, 100);
    }
  }, []);

  const onDisconnect = useCallback(() => {
    console.log('🔌 RN CarPlay onDisconnect triggered');
    sendErrorReport('false', 'carplay_connected_onDisconnect');
    setCarPlayConnected(false);
    setCarPlayForeground(false);
    setCarPlayStatus('disconnected');
  }, []);

  console.log('🚀 ~ CarPlayComponent ~ alertData:', alertData);
  useEffect(() => {
    if (alertData && alertData.message) {
      sendErrorReport(alertData, 'alertData_carplay');
      handleNotification({
        title: alertData?.title,
        message: alertData?.message,
        type: 'alert',
        actions: alertData?.actions || [],
      });
    }
  }, [alertData, handleNotification]);

  // Handle notifications
  const handleNotification = useCallback(
    notification => {
      sendErrorReport(notification, 'carplay_notification');
      sendErrorReport(
        CarPlay.connected,
        'carplay_connected_handleNotification',
      );

      try {
        const { title, message, type = 'alert', actions = [] } = notification;
        // Use CarPlay.connected as the primary source of truth
        const isCarPlayReady = CarPlay.connected;
        if (!isCarPlayReady) {
          pendingNotification.current = notification;
          return;
        }
        sendErrorReport(carPlayForeground, 'carplay_foreground');
        console.log('carPlayForegroundRef.current', carPlayForeground);
        if (!carPlayForeground) {
          if (MaxRCTCarPlayNotificationManager) {
            MaxRCTCarPlayNotificationManager.sendCarPlayNotification(
              title || 'Alert',
              message,
            )
              .then(res => {
                sendErrorReport(res, 'carplay_banner_send');
                console.log('✅ CarPlay notification result:', res);
              })
              .catch(err => {
                sendErrorReport(err, 'carplay_banner_send_error');
                console.error('❌ Error sending CarPlay notification:', err);
              });
          } else {
            console.warn('❌ CarPlay notification manager not available');
          }
          return;
        }

        if (type === 'alert') {
          const alertTemplate = new AlertTemplate({
            titleVariants: [message],
            actions:
              actions.length > 0
                ? actions
                : [
                    {
                      id: 'ok',
                      title: 'OK',
                    },
                  ],
            onActionButtonPressed: ({ id }) => {
              CarPlay.dismissTemplate();
              if (notification.onActionPressed) {
                notification.onActionPressed(id);
              }
            },
          });

          sendErrorReport(notification, 'carplay_notification_alert');

          // Try to present the template
          try {
            CarPlay.presentTemplate(alertTemplate, true);
            console.log('✅ AlertTemplate presented successfully');
          } catch (error) {
            console.error('❌ Failed to present AlertTemplate:', error);
          }
        } else if (type === 'actionSheet') {
          console.log('📋 Creating ActionSheetTemplate');

          const actionSheetTemplate = new ActionSheetTemplate({
            title: title,
            message: message,
            actions:
              actions.length > 0
                ? actions
                : [
                    {
                      id: 'ok',
                      title: 'OK',
                    },
                    {
                      id: 'cancel',
                      title: 'Cancel',
                      style: 'cancel',
                    },
                  ],
            onActionButtonPressed: ({ id }) => {
              CarPlay.dismissTemplate();
              if (notification.onActionPressed) {
                notification.onActionPressed(id);
              }
            },
          });

          try {
            CarPlay.presentTemplate(actionSheetTemplate, true);
            console.log('✅ ActionSheetTemplate presented successfully');
          } catch (error) {
            console.error('❌ Failed to present ActionSheetTemplate:', error);
          }
        }
      } catch (error) {
        sendErrorReport(error, 'carplay_error_handleNotification');
        console.error('❌ Error handling CarPlay notification:', error);
        console.error('Error stack:', error.stack);
      }
    },
    [carPlayConnected, carPlayForeground],
  );

  // Enhanced CarPlay state monitoring
  useEffect(() => {
    const carPlayEmitter = new NativeEventEmitter(
      NativeModules.CarPlayEventEmitter,
    );

    const fg = carPlayEmitter.addListener('carPlayForeground', data => {
      console.log('🟢 CarPlay foreground detected:', data);
      sendErrorReport(data || 'true', 'CarPlay_foreground_detected');
      setCarPlayForeground(true);
      if (carPlayConnected) {
        setCarPlayStatus('foreground');
      }
    });

    const bg = carPlayEmitter.addListener('carPlayBackground', data => {
      console.log('🟡 CarPlay background detected:', data);
      sendErrorReport(data || 'true', 'CarPlay_background_detected');
      setCarPlayForeground(false);
      if (carPlayConnected) {
        setCarPlayStatus('background');
      }
    });

    const connected = carPlayEmitter.addListener('carPlayConnected', data => {
      console.log('🔗 CarPlay connected:', data);
      sendErrorReport(data || 'true', 'CarPlay_connected_detected');
      setCarPlayConnected(true);
      setCarPlayForeground(true);
      setCarPlayStatus('foreground');
    });

    const disconnected = carPlayEmitter.addListener(
      'carPlayDisconnected',
      data => {
        console.log('🔌 CarPlay disconnected:', data);
        sendErrorReport(data || 'true', 'CarPlay_disconnected_detected');
        setCarPlayConnected(false);
        setCarPlayForeground(false);
        setCarPlayStatus('disconnected');
      },
    );

    return () => {
      fg.remove();
      bg.remove();
      connected.remove();
      disconnected.remove();
    };
  }, [carPlayConnected]);

  // Set up CarPlay event listeners
  useEffect(() => {
    const emt = new NativeEventEmitter(NativeModules.RNCarPlay);
    const connectListener = emt.addListener('didConnect', onConnect);

    // Set up notification listener
    const notificationEmitter = new NativeEventEmitter(
      NativeModules.MaxRCTCarPlayNotificationManager,
    );

    const notificationListener = notificationEmitter.addListener(
      'carPlayNotification',
      notification => {
        handleNotification(notification);
      },
    );

    CarPlay.registerOnConnect(onConnect);
    CarPlay.registerOnDisconnect(onDisconnect);

    return () => {
      connectListener.remove();
      notificationListener?.remove();
      CarPlay.unregisterOnConnect(onConnect);
      CarPlay.unregisterOnDisconnect(onDisconnect);
    };
  }, [onConnect, onDisconnect]);

  // Set up main grid template
  useEffect(() => {
    const infoTemplate = new InformationTemplate({
      title: `ChillBaby${user?.full_name ? ` - ${user.full_name}` : ''}`,
      subtitle: 'Your personalized dashboard in CarPlay',
      actions: [], // you can add actions if needed
      items: [
        {
          title: 'Welcome',
          detail: user?.full_name || 'Guest User',
        },
        {
          title: 'Status',
          detail: carPlayConnected
            ? '✅ Connected to CarPlay'
            : '❌ Not Connected',
        },
      ],
    });

    CarPlay.setRootTemplate(infoTemplate);
  }, [carPlayConnected, user, handleNotification]);

  return <View></View>;
};

export default CarPlayComponent;
