//
//  MaxRCTCarPlayNotificationManager.m
//  maxicosi
//
//  Created by <PERSON><PERSON><PERSON> on 19/11/2024.
//

#import <Foundation/Foundation.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>
 
@interface RCT_EXTERN_MODULE(MaxRCTCarPlayNotificationManager, RCTEventEmitter)

RCT_EXTERN_METHOD(
  sendCarPlayNotification:(NSString *)title
  body:(NSString *)body
  resolver:(RCTPromiseResolveBlock)resolve
  rejecter:(RCTPromiseRejectBlock)reject
)

RCT_EXTERN_METHOD(
  getCarPlayConnectionStatus:(RCTPromiseResolveBlock)resolve
  rejecter:(RCTPromiseRejectBlock)reject
)
 
// Expose the logMethod
RCT_EXTERN_METHOD(logMethod)
 
@end
