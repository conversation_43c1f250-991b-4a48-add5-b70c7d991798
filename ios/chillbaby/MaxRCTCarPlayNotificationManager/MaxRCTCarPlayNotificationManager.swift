import React
import UserNotifications
import UIKit
import CarPlay

@objc(MaxRCTCarPlayNotificationManager)
class MaxRCTCarPlayNotificationManager: RCTEventEmitter {
    
    private var hasListeners = false
    
    override static func moduleName() -> String! {
        return "MaxRCTCarPlayNotificationManager"
    }
    
    override init() {
        super.init()
        configureCarPlayNotifications()
    }
    
    override static func requiresMainQueueSetup() -> Bool {
        return true
    }
    
    override func supportedEvents() -> [String]! {
        return ["carPlayNotification", "carPlayStateChanged"]
    }
    
    override func startObserving() {
        hasListeners = true
    }
    
    override func stopObserving() {
        hasListeners = false
    }
    
    private func configureCarPlayNotifications() {
        // Define actions
        let acceptAction = UNNotificationAction(
            identifier: "ACCEPT_ACTION",
            title: "Accept",
            options: [.foreground]
        )
        let declineAction = UNNotificationAction(
            identifier: "DECLINE_ACTION",
            title: "Decline",
            options: [.destructive]
        )
        // Define category
        let carPlayCategory = UNNotificationCategory(
            identifier: "CARPLAY_CATEGORY_CBT",
            actions: [acceptAction, declineAction],
            intentIdentifiers: [],
            options: [.allowInCarPlay] // ✅ Ensures CarPlay eligibility
        )

        UNUserNotificationCenter.current().setNotificationCategories([carPlayCategory])
    }
    
    @objc func sendCarPlayNotification(_ title: String, body: String, resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
        print("🚗 Sending CarPlay notification: \(title) - \(body)")
        
        // Check CarPlay connection and state
        let carPlayState = getCarPlayState()
        print("CarPlay State: \(carPlayState)")
        
        // Set up the notification content
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.categoryIdentifier = "CARPLAY_CATEGORY_CBT"
        content.sound = UNNotificationSound.default
        
        // Add custom data to identify this as a CarPlay notification
        content.userInfo = [
            "carPlayNotification": true,
            "carPlayState": carPlayState
        ]

        // Define a unique identifier
        let request = UNNotificationRequest(identifier: UUID().uuidString, content: content, trigger: nil)
        
        // Schedule the notification
        UNUserNotificationCenter.current().add(request) { error in
            DispatchQueue.main.async {
                if let error = error {
                    print("Error adding CarPlay notification: \(error)")
                    rejecter("CARPLAY_ERROR", "Failed to send notification", error)
                } else {
                    print("CarPlay notification sent successfully!")
                    resolver([
                        "success": true,
                        "carPlayState": carPlayState,
                        "message": "Notification sent"
                    ])
                }
            }
        }
    }
    
    @objc func getCarPlayConnectionStatus(_ resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
        let state = getCarPlayState()
        resolver([
            "connected": state["connected"] as Any,
            "foreground": state["foreground"] as Any,
            "state": state
        ])
    }
    
    private func getCarPlayState() -> [String: Any] {
        // Check if CarPlay scene exists and is connected
        let carPlayScenes = UIApplication.shared.connectedScenes.filter { 
            $0.session.role == .carTemplateApplication 
        }
        
        let isConnected = !carPlayScenes.isEmpty
        var isForeground = false
        
        if let carPlayScene = carPlayScenes.first {
            isForeground = carPlayScene.activationState == .foregroundActive
            print("CarPlay scene found - State: \(carPlayScene.activationState.rawValue)")
        }
        
        let state = [
            "connected": isConnected,
            "foreground": isForeground,
            "scenesCount": carPlayScenes.count
        ] as [String : Any]
        
        return state
    }
    
    @objc func logMethod() {
        print("logMethod was called from React Native!")
        let state = getCarPlayState()
        print("Current CarPlay state: \(state)")
    }
}
